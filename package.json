{"name": "astrolus", "type": "module", "version": "0.2.1", "private": true, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "lint": "eslint .", "lint:fix": "eslint . --fix", "netlify": "netlify dev", "netlify:build": "netlify build"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/netlify": "^6.5.6", "@astrojs/node": "^9.4.0", "@astrojs/react": "^4.3.0", "@astrojs/sitemap": "^3.4.2", "@google-cloud/talent": "^7.0.1", "@google/genai": "^1.13.0", "@google/generative-ai": "^0.24.1", "@iconify-json/mdi": "^1.2.3", "@mistralai/mistralai": "^1.7.5", "@nanostores/preact": "^1.0.0", "@netlify/emails": "^1.1.0", "@react-email/render": "^1.2.0", "@sendgrid/mail": "^8.1.5", "@types/cheerio": "^1.0.0", "@types/dompurify": "^3.2.0", "@types/uuid": "^10.0.0", "astro-font": "^1.1.0", "astro-icon": "^1.1.5", "axios": "^1.11.0", "cheerio": "^1.1.2", "crypto": "^1.0.1", "deno": "^2.4.3", "dompurify": "^3.2.6", "dotenv": "^17.2.1", "esbuild": "^0.25.8", "find-up": "^7.0.0", "firebase": "^12.1.0", "google-auth-library": "^10.2.1", "googleapis": "^155.0.1", "groq-sdk": "^0.30.0", "llamaapi": "^1.0.0", "locate-path": "^7.2.0", "lodash": "^4.17.21", "mammoth": "^1.10.0", "nanostores": "^1.0.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "openai": "^5.12.2", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.4.54", "pdfreader": "^3.0.7", "puppeteer": "^24.16.0", "puppeteer-core": "^24.16.0", "razorpay": "^2.9.5", "react": "^19.1.1", "react-dom": "^19.1.1", "resend": "^6.0.1", "tesseract.js": "^6.0.1", "textract": "^2.5.0", "uuid": "^11.0.5", "zod": "^3.25.64"}, "devDependencies": {"@astrojs/tailwind": "^6.0.0", "@eslint/js": "^9.33.0", "@iconify-json/lucide": "^1.2.61", "@iconify/tailwind": "^1.2.0", "@netlify/functions": "^4.2.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.2.1", "@types/nodemailer": "^6.4.17", "@types/pdf-parse": "^1.1.5", "@types/textract": "^2.4.5", "astro": "^5.12.9", "autoprefixer": "^10.4.21", "browserify-fs": "^1.0.0", "crypto-browserify": "^3.12.1", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-astro": "^1.3.1", "events": "^3.3.0", "firebase-admin": "^13.4.0", "firebase-functions": "^6.4.0", "path-browserify": "^1.0.1", "postcss": "^8.5.5", "stream-browserify": "^3.0.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2", "typescript-eslint": "^8.39.0", "util": "^0.12.5", "vite-plugin-node-polyfills": "^0.24.0"}}